# AirLink Panel - CasaOS App

This folder contains the CasaOS app configuration for AirLink Panel, a streamlined game server management platform.

## What is AirLink Panel?

AirLink Panel is an advanced, open-source game server management platform designed to simplify server deployment, monitoring, and administration. With its powerful addon system, you can extend the functionality of the panel to suit your specific needs.

## Features

- 🎮 **Game Server Management** - Deploy and manage multiple game servers
- 🐳 **Docker Integration** - Container-based server deployment
- 👥 **User Management** - Multi-user support with role-based permissions
- 🔧 **Addon System** - Extend functionality with custom addons
- 📊 **Real-time Monitoring** - Live server statistics and performance metrics
- 🌐 **Web Interface** - Modern, responsive web-based control panel
- 🔒 **Security** - Built-in authentication and authorization

## Installation on CasaOS

### Method 1: Using the App Store (Recommended)
1. Open CasaOS App Store
2. Search for "AirLink Panel"
3. Click Install
4. Configure the settings as needed
5. Start the application

### Method 2: Manual Installation
1. Copy the `panel` folder to `/DATA/AppData/airlink-panel/`
2. Import the `casa.yml` file in CasaOS
3. Start the application

### Method 3: Docker Compose
1. Copy this entire `casaos-app` folder to your CasaOS system
2. Navigate to the folder in terminal
3. Run: `docker-compose up -d`

## Configuration

### Environment Variables
- `NODE_ENV`: Set to `production` for production deployment
- `PORT`: Port number for the web interface (default: 3000)
- `DATABASE_URL`: Database connection string (default: SQLite file)
- `NAME`: Display name for the panel (default: "AirLink Panel")

### Volumes
- `/app/storage`: Persistent storage for database, logs, and user data
- `/app/node_modules`: Node.js dependencies (cached for faster startups)

### Ports
- `3000`: Web interface port (HTTP)

## First Time Setup

1. After installation, access the panel at `http://your-casa-ip:3000`
2. Create your admin account on the first visit
3. Configure your game server nodes in the admin panel
4. Set up your first game server

## File Structure

```
casaos-app/
├── casa.yml              # CasaOS app configuration
├── docker-compose.yml    # Docker Compose configuration
└── README.md            # This file
```

## Requirements

- **CPU**: 1+ cores recommended
- **RAM**: 512MB minimum, 1GB+ recommended
- **Storage**: 2GB+ for the application, additional space for game servers
- **Network**: Internet access for downloading game server files

## Supported Game Servers

AirLink Panel supports various game servers through its Docker-based deployment system:
- Minecraft (Java & Bedrock)
- Counter-Strike servers
- Garry's Mod
- Rust
- And many more through custom Docker images

## Troubleshooting

### Application Won't Start
1. Check the container logs: `docker logs airlink-panel`
2. Ensure sufficient disk space is available
3. Verify the panel folder is properly mounted
4. Check if port 3000 is already in use

### Database Issues
1. The default SQLite database is stored in the `storage` volume
2. For production use, consider configuring PostgreSQL or MySQL
3. Database migrations run automatically on startup

### Performance Issues
1. Increase memory allocation if running many game servers
2. Consider using SSD storage for better I/O performance
3. Monitor CPU usage during peak times

## Support

- **Documentation**: [AirLink Panel Docs](https://github.com/AirlinkLabs/panel/tree/main/docs)
- **Discord**: [Join our Discord](https://discord.gg/D8YbT9rDqz)
- **GitHub Issues**: [Report Issues](https://github.com/AirlinkLabs/panel/issues)

## License

AirLink Panel is open-source software. Please check the main repository for license details.

## Contributing

Contributions are welcome! Please visit the main [AirLink Panel repository](https://github.com/AirlinkLabs/panel) for contribution guidelines.
