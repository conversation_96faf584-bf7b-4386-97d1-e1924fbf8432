#!/bin/bash

# CasaOS Test Environment Setup Script for Linux/macOS
# This script sets up a CasaOS test environment using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        echo "Visit: https://docs.docker.com/get-docker/"
        exit 1
    fi
    
    if ! docker ps &> /dev/null; then
        log_error "Docker is not running or you don't have permission to access it."
        echo "Try: sudo systemctl start docker"
        echo "Or add your user to docker group: sudo usermod -aG docker \$USER"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
}

show_status() {
    echo ""
    log_info "CasaOS Test Environment Status"
    echo "================================="
    
    # Check Docker
    if command -v docker &> /dev/null; then
        log_success "Docker: Installed"
        if docker ps &> /dev/null; then
            log_success "Docker: Running"
        else
            log_error "Docker: Not Running"
        fi
    else
        log_error "Docker: Not Installed"
        return
    fi
    
    # Check containers
    echo ""
    log_info "Container Status:"
    docker ps -a --filter "name=casaos-test" --filter "name=portainer-test" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" || log_warning "No test containers found"
    
    # Check volumes
    echo ""
    log_info "Volume Status:"
    docker volume ls --filter "name=casaos-test" --format "table {{.Name}}\t{{.Driver}}" || log_warning "No test volumes found"
    
    echo ""
    log_info "Access URLs:"
    echo "CasaOS Web UI: http://localhost"
    echo "Portainer: http://localhost:9000"
    echo "AirLink Panel: http://localhost:3000"
}

start_casaos() {
    echo ""
    log_info "Starting CasaOS Test Environment"
    echo "================================="
    
    check_docker
    
    # Check if panel directory exists
    if [ ! -d "../panel" ]; then
        log_warning "Panel directory not found at ../panel"
        log_warning "The AirLink Panel source will not be available in CasaOS"
    fi
    
    # Determine docker-compose command
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        DOCKER_COMPOSE_CMD="docker compose"
    fi
    
    # Start services
    log_info "Starting containers..."
    $DOCKER_COMPOSE_CMD up -d
    
    log_success "Containers started successfully!"
    
    log_info "Waiting for services to initialize..."
    sleep 10
    
    echo ""
    log_info "Access Information:"
    echo "CasaOS Web UI: http://localhost"
    echo "Portainer: http://localhost:9000 (admin/admin123)"
    echo "AirLink Panel: http://localhost:3000 (when installed)"
    
    echo ""
    log_info "Next Steps:"
    echo "1. Access CasaOS at http://localhost"
    echo "2. Complete the initial setup"
    echo "3. Import the casa.yml file from ../casaos-app/"
    echo "4. Install and test the AirLink Panel app"
}

stop_casaos() {
    echo ""
    log_info "Stopping CasaOS Test Environment"
    echo "================================="
    
    # Determine docker-compose command
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        DOCKER_COMPOSE_CMD="docker compose"
    fi
    
    $DOCKER_COMPOSE_CMD down
    log_success "Containers stopped successfully!"
}

show_logs() {
    echo ""
    log_info "CasaOS Test Environment Logs"
    echo "============================"
    
    echo ""
    log_info "CasaOS Logs:"
    docker logs casaos-test --tail 50 || log_error "Error getting CasaOS logs"
    
    echo ""
    log_info "Portainer Logs:"
    docker logs portainer-test --tail 20 || log_error "Error getting Portainer logs"
}

clean_environment() {
    echo ""
    log_info "Cleaning CasaOS Test Environment"
    echo "================================"
    
    log_warning "This will remove all containers, volumes, and data!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # Determine docker-compose command
        if command -v docker-compose &> /dev/null; then
            DOCKER_COMPOSE_CMD="docker-compose"
        else
            DOCKER_COMPOSE_CMD="docker compose"
        fi
        
        # Stop and remove containers
        $DOCKER_COMPOSE_CMD down -v --remove-orphans
        
        # Remove volumes
        docker volume rm casaos-test_casaos_config casaos-test_casaos_data casaos-test_casaos_apps casaos-test_portainer_data 2>/dev/null || true
        
        log_success "Environment cleaned successfully!"
    else
        log_warning "Cleanup cancelled"
    fi
}

show_help() {
    echo ""
    echo "🏠 CasaOS Test Environment Manager"
    echo "================================="
    echo ""
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  start     Start CasaOS test environment (default)"
    echo "  stop      Stop the test environment"
    echo "  status    Show environment status"
    echo "  logs      Show container logs"
    echo "  clean     Clean up everything (removes all data)"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Start CasaOS"
    echo "  $0 start        # Start CasaOS"
    echo "  $0 status       # Show status"
    echo "  $0 logs         # Show logs"
    echo "  $0 stop         # Stop services"
    echo "  $0 clean        # Clean everything"
}

# Main script logic
case "${1:-start}" in
    "start")
        start_casaos
        ;;
    "stop")
        stop_casaos
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "clean")
        clean_environment
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        log_error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
