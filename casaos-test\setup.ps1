# CasaOS Test Environment Setup Script for Windows
# This script sets up a CasaOS test environment using Docker

param(
    [switch]$Clean,
    [switch]$Logs,
    [switch]$Stop,
    [switch]$Status
)

# Colors for output
$Red = "Red"
$Green = "Green"
$Yellow = "Yellow"
$Blue = "Cyan"

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Test-DockerInstalled {
    try {
        docker --version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Test-DockerRunning {
    try {
        docker ps | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

function Show-Status {
    Write-ColorOutput "🔍 CasaOS Test Environment Status" $Blue
    Write-ColorOutput "=================================" $Blue
    
    # Check Docker
    if (Test-DockerInstalled) {
        Write-ColorOutput "✅ Docker: Installed" $Green
        if (Test-DockerRunning) {
            Write-ColorOutput "✅ Docker: Running" $Green
        } else {
            Write-ColorOutput "❌ Docker: Not Running" $Red
        }
    } else {
        Write-ColorOutput "❌ Docker: Not Installed" $Red
        return
    }
    
    # Check containers
    Write-ColorOutput "`n📦 Container Status:" $Blue
    try {
        $containers = docker ps -a --filter "name=casaos-test" --filter "name=portainer-test" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        if ($containers) {
            Write-Host $containers
        } else {
            Write-ColorOutput "No test containers found" $Yellow
        }
    }
    catch {
        Write-ColorOutput "Error checking container status" $Red
    }
    
    # Check volumes
    Write-ColorOutput "`n💾 Volume Status:" $Blue
    try {
        $volumes = docker volume ls --filter "name=casaos-test" --format "table {{.Name}}\t{{.Driver}}"
        if ($volumes) {
            Write-Host $volumes
        } else {
            Write-ColorOutput "No test volumes found" $Yellow
        }
    }
    catch {
        Write-ColorOutput "Error checking volume status" $Red
    }
    
    Write-ColorOutput "`n🌐 Access URLs:" $Blue
    Write-ColorOutput "CasaOS Web UI: http://localhost" $Green
    Write-ColorOutput "Portainer: http://localhost:9000" $Green
    Write-ColorOutput "AirLink Panel: http://localhost:3000" $Green
}

function Start-CasaOS {
    Write-ColorOutput "🚀 Starting CasaOS Test Environment" $Blue
    Write-ColorOutput "===================================" $Blue
    
    # Check prerequisites
    if (-not (Test-DockerInstalled)) {
        Write-ColorOutput "❌ Docker is not installed. Please install Docker Desktop first." $Red
        Write-ColorOutput "Download from: https://www.docker.com/products/docker-desktop/" $Yellow
        return
    }
    
    if (-not (Test-DockerRunning)) {
        Write-ColorOutput "❌ Docker is not running. Please start Docker Desktop." $Red
        return
    }
    
    # Check if panel directory exists
    if (-not (Test-Path "../panel")) {
        Write-ColorOutput "⚠️  Panel directory not found at ../panel" $Yellow
        Write-ColorOutput "The AirLink Panel source will not be available in CasaOS" $Yellow
    }
    
    # Start services
    Write-ColorOutput "📦 Starting containers..." $Blue
    try {
        docker-compose up -d
        Write-ColorOutput "✅ Containers started successfully!" $Green
        
        Write-ColorOutput "`n⏳ Waiting for services to initialize..." $Yellow
        Start-Sleep -Seconds 10
        
        Write-ColorOutput "`n🌐 Access Information:" $Blue
        Write-ColorOutput "CasaOS Web UI: http://localhost" $Green
        Write-ColorOutput "Portainer: http://localhost:9000 (admin/admin123)" $Green
        Write-ColorOutput "AirLink Panel: http://localhost:3000 (when installed)" $Green
        
        Write-ColorOutput "`n📋 Next Steps:" $Blue
        Write-ColorOutput "1. Access CasaOS at http://localhost" $Green
        Write-ColorOutput "2. Complete the initial setup" $Green
        Write-ColorOutput "3. Import the casa.yml file from ../casaos-app/" $Green
        Write-ColorOutput "4. Install and test the AirLink Panel app" $Green
        
    }
    catch {
        Write-ColorOutput "❌ Error starting containers: $_" $Red
    }
}

function Stop-CasaOS {
    Write-ColorOutput "🛑 Stopping CasaOS Test Environment" $Blue
    Write-ColorOutput "====================================" $Blue
    
    try {
        docker-compose down
        Write-ColorOutput "✅ Containers stopped successfully!" $Green
    }
    catch {
        Write-ColorOutput "❌ Error stopping containers: $_" $Red
    }
}

function Show-Logs {
    Write-ColorOutput "📋 CasaOS Test Environment Logs" $Blue
    Write-ColorOutput "================================" $Blue
    
    Write-ColorOutput "`n🔍 CasaOS Logs:" $Blue
    try {
        docker logs casaos-test --tail 50
    }
    catch {
        Write-ColorOutput "❌ Error getting CasaOS logs: $_" $Red
    }
    
    Write-ColorOutput "`n🔍 Portainer Logs:" $Blue
    try {
        docker logs portainer-test --tail 20
    }
    catch {
        Write-ColorOutput "❌ Error getting Portainer logs: $_" $Red
    }
}

function Clean-Environment {
    Write-ColorOutput "🧹 Cleaning CasaOS Test Environment" $Blue
    Write-ColorOutput "====================================" $Blue
    
    Write-ColorOutput "⚠️  This will remove all containers, volumes, and data!" $Yellow
    $confirm = Read-Host "Are you sure? (y/N)"
    
    if ($confirm -eq "y" -or $confirm -eq "Y") {
        try {
            # Stop and remove containers
            docker-compose down -v --remove-orphans
            
            # Remove volumes
            docker volume rm casaos-test_casaos_config casaos-test_casaos_data casaos-test_casaos_apps casaos-test_portainer_data -f
            
            Write-ColorOutput "✅ Environment cleaned successfully!" $Green
        }
        catch {
            Write-ColorOutput "❌ Error cleaning environment: $_" $Red
        }
    } else {
        Write-ColorOutput "❌ Cleanup cancelled" $Yellow
    }
}

# Main script logic
Write-ColorOutput "🏠 CasaOS Test Environment Manager" $Blue
Write-ColorOutput "===================================" $Blue

if ($Status) {
    Show-Status
}
elseif ($Logs) {
    Show-Logs
}
elseif ($Stop) {
    Stop-CasaOS
}
elseif ($Clean) {
    Clean-Environment
}
else {
    Start-CasaOS
}

Write-ColorOutput "`n💡 Usage Examples:" $Blue
Write-ColorOutput ".\setup.ps1          # Start CasaOS" $Green
Write-ColorOutput ".\setup.ps1 -Status  # Show status" $Green
Write-ColorOutput ".\setup.ps1 -Logs    # Show logs" $Green
Write-ColorOutput ".\setup.ps1 -Stop    # Stop services" $Green
Write-ColorOutput ".\setup.ps1 -Clean   # Clean everything" $Green
