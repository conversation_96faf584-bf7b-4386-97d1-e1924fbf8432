name: airlink-panel
services:
  airlink-panel:
    image: node:18-alpine
    container_name: airlink-panel
    restart: unless-stopped
    working_dir: /app
    environment:
      NODE_ENV: production
      PORT: "3000"
      DATABASE_URL: file:/app/storage/database.db
      NAME: AirLink Panel
    volumes:
      - type: bind
        source: /DATA/AppData/airlink-panel/panel
        target: /app
        read_only: true
      - type: volume
        source: airlink_storage
        target: /app/storage
      - type: volume
        source: airlink_node_modules
        target: /app/node_modules
    ports:
      - target: 3000
        published: "3000"
        protocol: tcp
    command: >
      sh -c "
        if [ ! -f /app/node_modules/.installed ]; then
          npm install --omit=dev &&
          touch /app/node_modules/.installed
        fi &&
        if [ ! -f /app/dist/app.js ]; then
          npm run build-ts
        fi &&
        npx prisma migrate deploy &&
        npm run start
      "
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  airlink_storage:
    driver: local
  airlink_node_modules:
    driver: local

x-casaos:
  architectures:
    - amd64
    - arm64
  main: airlink-panel
  author: AirlinkLabs
  category: Developer
  description:
    en_us: "AirLink Panel is an advanced, open-source game server management platform designed to simplify server deployment, monitoring, and administration. With its powerful addon system, you can extend the functionality of the panel to suit your specific needs."
  developer: AirlinkLabs
  icon: https://raw.githubusercontent.com/AirlinkLabs/panel/main/panel/public/assets/logo.png
  screenshot_link:
    - https://raw.githubusercontent.com/AirlinkLabs/panel/main/docs/screenshots/dashboard.png
  tagline:
    en_us: Streamlined Game Server Management
  thumbnail: https://raw.githubusercontent.com/AirlinkLabs/panel/main/panel/public/assets/logo.png
  title:
    en_us: AirLink Panel
  index: /
  port_map: "3000"
  scheme: http
  store_app_id: airlink-panel
  tips:
    before_install:
      en_us: |
        Before installing AirLink Panel:
        1. Make sure you have sufficient disk space for game servers
        2. The panel will be accessible at http://your-casa-ip:3000
        3. Default database is SQLite, stored in the storage volume
        4. First run will take longer as it builds the application
    after_install:
      en_us: |
        After installation:
        1. Access the panel at http://your-casa-ip:3000
        2. Create your admin account on first visit
        3. Configure your game server nodes in the admin panel
        4. Check the logs if the service doesn't start properly
  changelog:
    en_us: |
      Initial CasaOS app release for AirLink Panel
      - Game server management interface
      - Docker-based server deployment
      - User management and permissions
      - Addon system support
      - Real-time server monitoring
