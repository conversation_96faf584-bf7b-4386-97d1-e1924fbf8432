# AirLink Panel Environment Configuration
# Copy this file to .env and modify the values as needed

# Application Settings
NODE_ENV=production
PORT=3000
NAME=AirLink Panel

# Database Configuration
# SQLite (default - recommended for CasaOS)
DATABASE_URL=file:/app/storage/database.db

# PostgreSQL (alternative - for advanced users)
# DATABASE_URL=postgresql://username:password@localhost:5432/airlink

# MySQL (alternative - for advanced users)  
# DATABASE_URL=mysql://username:password@localhost:3306/airlink

# Security Settings
SESSION_SECRET=your-super-secret-session-key-change-this
CSRF_SECRET=your-csrf-secret-key-change-this

# Optional: External Database Settings
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=airlink
# DB_USER=airlink
# DB_PASS=password

# Optional: Redis Session Store (for multiple instances)
# REDIS_URL=redis://localhost:6379

# Optional: Email Configuration (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password
# <AUTHOR> <EMAIL>

# Optional: File Upload Limits
# MAX_FILE_SIZE=10485760  # 10MB in bytes
# MAX_FILES=5

# Optional: Logging Configuration
# LOG_LEVEL=info
# LOG_FILE=/app/storage/logs/airlink.log

# Optional: Rate Limiting
# RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds
# RATE_LIMIT_MAX=100        # Max requests per window

# Optional: CORS Settings (if needed for API access)
# CORS_ORIGIN=https://yourdomain.com
# CORS_CREDENTIALS=true

# Optional: Backup Configuration
# BACKUP_ENABLED=true
# BACKUP_INTERVAL=86400000  # 24 hours in milliseconds
# BACKUP_RETENTION=7        # Keep 7 backups
