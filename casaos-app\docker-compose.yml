version: '3.8'

services:
  airlink-panel:
    image: node:18-alpine
    container_name: airlink-panel
    restart: unless-stopped
    working_dir: /app
    environment:
      - NODE_ENV=production
      - PORT=3000
      - DATABASE_URL=file:/app/storage/database.db
      - NAME=AirLink Panel
    volumes:
      - ./panel:/app:ro
      - airlink_storage:/app/storage
      - airlink_node_modules:/app/node_modules
    ports:
      - "3000:3000"
    command: >
      sh -c "
        if [ ! -f /app/node_modules/.installed ]; then
          npm install --omit=dev &&
          touch /app/node_modules/.installed
        fi &&
        if [ ! -f /app/dist/app.js ]; then
          npm run build-ts
        fi &&
        npx prisma migrate deploy &&
        npm run start
      "
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  airlink_storage:
    driver: local
  airlink_node_modules:
    driver: local

networks:
  default:
    name: airlink-network
