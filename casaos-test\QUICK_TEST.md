# Quick CasaOS App Testing Guide

Since Docker Desktop isn't currently running, here are alternative ways to test your AirLink Panel CasaOS app:

## 🎯 Testing Options

### Option 1: Validate Configuration Files
You can validate your CasaOS app configuration without running CasaOS:

1. **Check casa.yml syntax**:
   ```bash
   # Install yq (YAML processor) if not installed
   # Then validate the YAML syntax
   cat ../casaos-app/casa.yml
   ```

2. **Check docker-compose.yml**:
   ```bash
   # Validate Docker Compose syntax
   docker-compose -f ../casaos-app/docker-compose.yml config
   ```

### Option 2: Test Docker Compose Directly
You can test the Docker configuration without CasaOS:

1. **Start Docker Desktop** (from Windows Start menu)
2. **Wait for Docker to start** (green whale icon in system tray)
3. **Test the AirLink Panel container**:
   ```bash
   cd ../casaos-app
   docker-compose up -d
   ```
4. **Access the panel** at http://localhost:3000

### Option 3: Use Online CasaOS Validator
1. Visit the CasaOS community tools (if available)
2. Upload your `casa.yml` file for validation
3. Check for any configuration issues

### Option 4: Install CasaOS on a VM or Server
1. Set up a Linux VM (Ubuntu/Debian)
2. Install CasaOS using their official installer
3. Test your app configuration there

## 🔍 What to Test

### Configuration Validation
- [ ] YAML syntax is correct
- [ ] All required fields are present
- [ ] Port mappings are correct
- [ ] Volume mounts are properly configured
- [ ] Environment variables are set correctly

### Docker Image Testing
- [ ] Base image (node:18-alpine) pulls successfully
- [ ] Application builds without errors
- [ ] Dependencies install correctly
- [ ] Database migrations run successfully
- [ ] Web server starts on port 3000

### CasaOS Integration
- [ ] App appears in CasaOS app store
- [ ] Installation process completes
- [ ] App starts successfully
- [ ] Web interface is accessible
- [ ] Persistent data is maintained

## 🛠️ Manual Testing Steps

### 1. Start Docker Desktop
- Open Docker Desktop from Windows Start menu
- Wait for the green whale icon in system tray
- Verify with: `docker ps`

### 2. Test the Panel Directly
```bash
# Navigate to your panel directory
cd ../panel

# Install dependencies
npm install

# Build the application
npm run build-ts

# Run database migrations
npx prisma migrate dev

# Start the application
npm start
```

### 3. Test Docker Build
```bash
# Create a simple Dockerfile for testing
cd ../panel
docker build -t airlink-panel-test .
docker run -p 3000:3000 airlink-panel-test
```

### 4. Validate CasaOS Configuration
```bash
# Check the casa.yml structure
cd ../casaos-app
cat casa.yml

# Validate Docker Compose
docker-compose config
```

## 📋 Checklist for CasaOS App

### Required Files ✅
- [x] `casa.yml` - CasaOS app configuration
- [x] `docker-compose.yml` - Container configuration
- [x] `.env.example` - Environment template
- [x] `README.md` - Documentation

### Configuration Validation ✅
- [x] Proper YAML syntax
- [x] Correct port mappings (3000:3000)
- [x] Volume mounts configured
- [x] Environment variables set
- [x] Health checks included

### Metadata ✅
- [x] App name and description
- [x] Author information
- [x] Category (Developer)
- [x] Icon and screenshots
- [x] Installation instructions

## 🚀 Next Steps

1. **Start Docker Desktop** when you're ready to test
2. **Run the setup script** or manual Docker commands
3. **Test the AirLink Panel** functionality
4. **Submit to CasaOS App Store** when ready

## 💡 Tips

- The app configuration is already complete and ready to use
- All necessary files are in the `../casaos-app/` folder
- The Docker setup will automatically build and start the panel
- First startup may take 2-3 minutes for npm install and build

## 🆘 If You Need Help

1. Start Docker Desktop first
2. Try the manual Docker commands in the README
3. Check the logs for any errors
4. The configuration files are ready - just need Docker running to test them!
