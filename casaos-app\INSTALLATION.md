# AirLink Panel CasaOS Installation Guide

This guide will help you install AirLink Panel as a CasaOS app.

## 📁 What's Included

This `casaos-app` folder contains everything needed to run AirLink Panel on CasaOS:

- `casa.yml` - CasaOS app configuration file
- `docker-compose.yml` - Docker Compose configuration
- `.env.example` - Environment variables template
- `install.sh` - Automated installation script (Linux/macOS)
- `README.md` - Detailed documentation
- `INSTALLATION.md` - This installation guide

## 🚀 Installation Methods

### Method 1: CasaOS App Store (Recommended)
1. Open your CasaOS web interface
2. Go to the App Store
3. Search for "AirLink Panel"
4. Click Install and configure as needed

### Method 2: Import casa.yml
1. Copy the entire `casaos-app` folder to your CasaOS system
2. In CasaOS, go to App Store → Import
3. Select the `casa.yml` file
4. Configure the installation settings
5. Click Install

### Method 3: Manual Docker Compose
1. Copy the `casaos-app` folder to your CasaOS system
2. Copy your `panel` folder to `/DATA/AppData/airlink-panel/`
3. Navigate to the `casaos-app` folder
4. Run: `docker-compose up -d`

### Method 4: Automated Script (Linux only)
1. Copy the `casaos-app` folder to your CasaOS system
2. Make the script executable: `chmod +x install.sh`
3. Run the installation script: `./install.sh`

## 📋 Pre-Installation Requirements

### System Requirements
- CasaOS installed and running
- Docker and Docker Compose available
- At least 1GB RAM available
- 2GB+ free disk space

### File Preparation
Before installation, ensure you have:
1. The AirLink Panel source code (the `panel` folder)
2. Sufficient permissions to create directories in `/DATA/AppData/`

## ⚙️ Configuration

### Environment Variables
Copy `.env.example` to `.env` and modify:

```bash
# Basic Configuration
NODE_ENV=production
PORT=3000
NAME=AirLink Panel
DATABASE_URL=file:/app/storage/database.db

# Security (generate random values)
SESSION_SECRET=your-random-session-secret
CSRF_SECRET=your-random-csrf-secret
```

### Volume Mapping
The app uses these volume mappings:
- `/DATA/AppData/airlink-panel/panel` → `/app` (panel source code)
- `airlink_storage` → `/app/storage` (persistent data)
- `airlink_node_modules` → `/app/node_modules` (dependencies cache)

### Port Configuration
- Default port: `3000`
- Accessible at: `http://your-casa-ip:3000`
- Make sure port 3000 is not used by other services

## 🔧 Post-Installation Setup

### First Time Access
1. Wait for the container to fully start (may take 2-3 minutes)
2. Access `http://your-casa-ip:3000`
3. Create your admin account
4. Configure your first game server node

### Checking Status
```bash
# Check if container is running
docker ps | grep airlink-panel

# View logs
docker logs airlink-panel

# Check resource usage
docker stats airlink-panel
```

## 🛠️ Troubleshooting

### Container Won't Start
1. Check logs: `docker logs airlink-panel`
2. Verify panel source is in `/DATA/AppData/airlink-panel/panel`
3. Ensure port 3000 is available
4. Check disk space availability

### Build Failures
1. Ensure Node.js dependencies can be installed
2. Check internet connectivity for npm packages
3. Verify sufficient disk space for build process

### Database Issues
1. Check if storage volume is properly mounted
2. Verify write permissions on storage directory
3. For SQLite issues, check file permissions

### Performance Issues
1. Increase memory allocation in Docker settings
2. Use SSD storage for better I/O performance
3. Monitor CPU usage during peak times

## 📊 Monitoring

### Health Checks
The container includes health checks that verify:
- Web server is responding on port 3000
- Application is properly initialized
- Database connectivity

### Log Locations
- Container logs: `docker logs airlink-panel`
- Application logs: `/DATA/AppData/airlink-panel/storage/logs/`
- Database: `/DATA/AppData/airlink-panel/storage/database.db`

## 🔄 Updates

### Updating the Application
1. Stop the container: `docker-compose down`
2. Update the panel source code
3. Restart: `docker-compose up -d`
4. The container will rebuild automatically if needed

### Updating Docker Image
1. Pull latest Node.js image: `docker pull node:18-alpine`
2. Recreate container: `docker-compose up -d --force-recreate`

## 🔒 Security Considerations

### Default Security Features
- CSRF protection enabled
- Rate limiting configured
- Helmet.js security headers
- Session-based authentication

### Recommended Security Steps
1. Change default session and CSRF secrets
2. Use HTTPS in production (reverse proxy)
3. Regular backups of the storage volume
4. Keep the Node.js base image updated

## 📞 Support

If you encounter issues:

1. **Check the logs** first: `docker logs airlink-panel`
2. **Verify configuration** in your `.env` file
3. **Check system resources** (RAM, disk space, CPU)
4. **Visit the community**:
   - Discord: https://discord.gg/D8YbT9rDqz
   - GitHub Issues: https://github.com/AirlinkLabs/panel/issues
   - Documentation: https://github.com/AirlinkLabs/panel/tree/main/docs

## 📝 Notes

- First startup takes longer due to npm install and build process
- The application automatically runs database migrations
- SQLite database is used by default for simplicity
- For production use, consider PostgreSQL or MySQL
- Regular backups of the storage volume are recommended
