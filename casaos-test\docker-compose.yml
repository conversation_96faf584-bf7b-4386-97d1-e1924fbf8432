version: '3.8'

services:
  casaos:
    image: casaos/casaos:latest
    container_name: casaos-test
    restart: unless-stopped
    privileged: true
    network_mode: host
    volumes:
      # CasaOS system directories
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - casaos_config:/casaos/config
      - casaos_data:/DATA
      - casaos_apps:/var/lib/casaos
      # Mount our airlink panel for testing
      - ../panel:/DATA/AppData/airlink-panel/panel:ro
      - ../casaos-app:/DATA/AppData/casaos-apps/airlink-panel:ro
    environment:
      - CASAOS_DEBUG=true
      - PUID=1000
      - PGID=1000
    ports:
      - "80:80"       # CasaOS Web UI
      - "443:443"     # HTTPS (if configured)
      - "3000:3000"   # AirLink Panel
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Optional: Portainer for Docker management
  portainer:
    image: portainer/portainer-ce:latest
    container_name: portainer-test
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    ports:
      - "9000:9000"
    command: --admin-password='$$2y$$10$$N8X8WdFzJjKtTOdEQf9ZOeKz8WK8WK8WK8WK8WK8WK8WK8WK8WK8W'
    # Default admin password: admin123 (change this!)

volumes:
  casaos_config:
    driver: local
  casaos_data:
    driver: local
  casaos_apps:
    driver: local
  portainer_data:
    driver: local

networks:
  default:
    name: casaos-test-network
