#!/bin/bash

# AirLink Panel CasaOS Installation Script
# This script helps set up AirLink Panel for CasaOS

set -e

echo "🚀 AirLink Panel CasaOS Installation Script"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CASAOS_DATA_DIR="/DATA/AppData/airlink-panel"
PANEL_SOURCE_DIR="../panel"
COMPOSE_FILE="docker-compose.yml"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Checking requirements..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if CasaOS data directory exists
    if [ ! -d "/DATA" ]; then
        log_error "CasaOS data directory (/DATA) not found. Are you running this on CasaOS?"
        exit 1
    fi
    
    log_success "Requirements check passed!"
}

setup_directories() {
    log_info "Setting up directories..."
    
    # Create CasaOS app data directory
    mkdir -p "$CASAOS_DATA_DIR"
    
    # Copy panel source if it exists
    if [ -d "$PANEL_SOURCE_DIR" ]; then
        log_info "Copying panel source code..."
        cp -r "$PANEL_SOURCE_DIR" "$CASAOS_DATA_DIR/"
        log_success "Panel source copied to $CASAOS_DATA_DIR/panel"
    else
        log_warning "Panel source directory not found at $PANEL_SOURCE_DIR"
        log_info "You'll need to manually copy the panel source to $CASAOS_DATA_DIR/panel"
    fi
    
    # Set proper permissions
    chown -R 1000:1000 "$CASAOS_DATA_DIR" 2>/dev/null || true
    
    log_success "Directories set up successfully!"
}

setup_environment() {
    log_info "Setting up environment configuration..."
    
    ENV_FILE="$CASAOS_DATA_DIR/.env"
    
    if [ ! -f "$ENV_FILE" ]; then
        # Copy example environment file
        cp .env.example "$ENV_FILE"
        
        # Generate random secrets
        SESSION_SECRET=$(openssl rand -hex 32 2>/dev/null || head -c 32 /dev/urandom | base64)
        CSRF_SECRET=$(openssl rand -hex 32 2>/dev/null || head -c 32 /dev/urandom | base64)
        
        # Update environment file with generated secrets
        sed -i "s/your-super-secret-session-key-change-this/$SESSION_SECRET/" "$ENV_FILE"
        sed -i "s/your-csrf-secret-key-change-this/$CSRF_SECRET/" "$ENV_FILE"
        
        log_success "Environment file created with random secrets"
    else
        log_info "Environment file already exists, skipping..."
    fi
}

install_app() {
    log_info "Installing AirLink Panel..."
    
    # Check if we're using docker-compose or docker compose
    if command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        DOCKER_COMPOSE_CMD="docker compose"
    fi
    
    # Update docker-compose.yml to use correct paths
    sed "s|./panel|$CASAOS_DATA_DIR/panel|g" "$COMPOSE_FILE" > "$CASAOS_DATA_DIR/docker-compose.yml"
    
    # Start the application
    cd "$CASAOS_DATA_DIR"
    $DOCKER_COMPOSE_CMD up -d
    
    log_success "AirLink Panel installation completed!"
}

show_completion_info() {
    echo ""
    echo "🎉 Installation Complete!"
    echo "========================"
    echo ""
    echo "AirLink Panel has been installed and should be starting up."
    echo ""
    echo "📍 Access URL: http://$(hostname -I | awk '{print $1}'):3000"
    echo "📁 Data Directory: $CASAOS_DATA_DIR"
    echo "📋 Logs: docker logs airlink-panel"
    echo ""
    echo "⏳ First startup may take a few minutes as the application builds."
    echo ""
    echo "🔧 Next Steps:"
    echo "1. Wait for the application to fully start (check logs if needed)"
    echo "2. Access the web interface and create your admin account"
    echo "3. Configure your game server nodes"
    echo "4. Start deploying game servers!"
    echo ""
    echo "📚 Documentation: https://github.com/AirlinkLabs/panel"
    echo "💬 Support: https://discord.gg/D8YbT9rDqz"
}

# Main installation process
main() {
    echo ""
    log_info "Starting AirLink Panel installation for CasaOS..."
    echo ""
    
    check_requirements
    setup_directories
    setup_environment
    install_app
    show_completion_info
}

# Run main function
main "$@"
