# CasaOS Test Environment

This folder contains everything needed to set up a local CasaOS test environment for testing the AirLink Panel CasaOS app.

## 🎯 Purpose

This test environment allows you to:
- Test the AirLink Panel CasaOS app locally
- Verify the casa.yml configuration works correctly
- Debug installation and runtime issues
- Develop and iterate on the CasaOS app configuration

## 📁 What's Included

- `docker-compose.yml` - CasaOS and Portainer containers
- `setup.ps1` - Windows PowerShell setup script
- `setup.sh` - Linux/macOS bash setup script
- `README.md` - This documentation

## 🚀 Quick Start

### Option 1: Start Docker Desktop First
1. **Start Docker Desktop** from your Windows Start menu
2. Wait for Docker to fully start (green icon in system tray)
3. Then run the setup script:

```powershell
# Windows (PowerShell)
.\setup.ps1

# Check status
.\setup.ps1 -Status

# View logs
.\setup.ps1 -Logs

# Stop environment
.\setup.ps1 -Stop

# Clean everything
.\setup.ps1 -Clean
```

### Option 2: Manual Docker Commands
If the script doesn't work, you can run Docker commands directly:

```powershell
# Start the services
docker-compose up -d

# Check status
docker ps

# View logs
docker logs casaos-test

# Stop services
docker-compose down
```

### Linux/macOS (Bash)
```bash
# Make script executable
chmod +x setup.sh

# Start CasaOS test environment
./setup.sh start

# Check status
./setup.sh status

# View logs
./setup.sh logs

# Stop environment
./setup.sh stop

# Clean everything
./setup.sh clean
```

## 📋 Prerequisites

### Required
- **Docker Desktop** (Windows/macOS) or **Docker Engine** (Linux)
- **Docker Compose** (usually included with Docker Desktop)
- At least **2GB RAM** available for containers
- **5GB+ free disk space**

### Optional
- **WSL2** (Windows) for better performance
- **Git** for cloning repositories

## 🔧 Setup Process

### 1. Install Docker
- **Windows/macOS**: Download Docker Desktop from https://www.docker.com/products/docker-desktop/
- **Linux**: Follow your distribution's Docker installation guide

### 2. Verify Docker Installation
```bash
docker --version
docker-compose --version
docker ps
```

### 3. Start the Test Environment
Run the appropriate setup script for your platform:
- Windows: `.\setup.ps1`
- Linux/macOS: `./setup.sh start`

### 4. Access CasaOS
- Open your browser to http://localhost
- Complete the CasaOS initial setup
- Create an admin account

## 🌐 Access URLs

Once running, you can access:

| Service | URL | Credentials |
|---------|-----|-------------|
| CasaOS Web UI | http://localhost | Set during setup |
| Portainer | http://localhost:9000 | admin / admin123 |
| AirLink Panel | http://localhost:3000 | After app installation |

## 📦 Testing the AirLink Panel App

### Method 1: Import casa.yml
1. Access CasaOS at http://localhost
2. Go to App Store → Import
3. Upload the `casa.yml` file from `../casaos-app/`
4. Configure and install the app

### Method 2: Manual Docker Compose
1. The panel source is automatically mounted at `/DATA/AppData/airlink-panel/panel`
2. The casaos-app files are mounted at `/DATA/AppData/casaos-apps/airlink-panel`
3. You can test the Docker Compose configuration directly

## 🛠️ Troubleshooting

### Common Issues

#### Docker Not Running
```bash
# Windows: Start Docker Desktop
# Linux: Start Docker service
sudo systemctl start docker

# Check if Docker is running
docker ps
```

#### Port Conflicts
If ports 80, 443, 3000, or 9000 are already in use:
1. Stop the conflicting services
2. Or modify the ports in `docker-compose.yml`

#### Permission Issues (Linux)
```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again, or run:
newgrp docker
```

#### Container Won't Start
```bash
# Check logs
docker logs casaos-test
docker logs portainer-test

# Check system resources
docker system df
docker system prune  # Clean up if needed
```

### Debugging Steps

1. **Check container status**:
   ```bash
   docker ps -a
   ```

2. **View logs**:
   ```bash
   docker logs casaos-test --tail 50
   ```

3. **Check volumes**:
   ```bash
   docker volume ls
   docker volume inspect casaos-test_casaos_data
   ```

4. **Test connectivity**:
   ```bash
   curl http://localhost
   ```

## 📊 Monitoring

### Container Health
The containers include health checks:
- CasaOS: HTTP check on port 80
- Automatic restart on failure

### Resource Usage
Monitor resource usage:
```bash
docker stats casaos-test portainer-test
```

### Logs
Access logs in real-time:
```bash
docker logs -f casaos-test
```

## 🔄 Development Workflow

### Testing Changes
1. Make changes to the casa.yml or docker-compose.yml
2. Stop the environment: `./setup.sh stop`
3. Start again: `./setup.sh start`
4. Test the changes in CasaOS

### Iterating on the App
1. Modify files in `../casaos-app/`
2. The changes are automatically available in the test environment
3. Re-import or reinstall the app in CasaOS to test

## 🧹 Cleanup

### Temporary Cleanup
```bash
# Stop containers but keep data
./setup.sh stop
```

### Full Cleanup
```bash
# Remove everything (containers, volumes, data)
./setup.sh clean
```

### Manual Cleanup
```bash
# Stop and remove containers
docker-compose down -v

# Remove volumes
docker volume prune

# Remove unused images
docker image prune
```

## 📝 Notes

### Data Persistence
- CasaOS configuration is stored in Docker volumes
- Your AirLink Panel source is mounted read-only
- Database and user data persist between restarts

### Network Configuration
- Uses host networking for CasaOS compatibility
- All services are accessible on localhost
- No external network access required

### Security Considerations
- This is for testing only - not production ready
- Default passwords should be changed
- No HTTPS configured by default
- Privileged mode required for CasaOS functionality

## 🆘 Getting Help

If you encounter issues:

1. **Check the logs** first using the setup scripts
2. **Verify Docker** is running and accessible
3. **Check system resources** (RAM, disk space)
4. **Review the troubleshooting section** above
5. **Ask for help** in the AirLink Discord or GitHub issues

## 🔗 Related Links

- [CasaOS Documentation](https://casaos.io/docs/)
- [Docker Documentation](https://docs.docker.com/)
- [AirLink Panel Repository](https://github.com/AirlinkLabs/panel)
- [CasaOS App Store Guidelines](https://github.com/CasaOS-Team/CasaOS-AppStore)
